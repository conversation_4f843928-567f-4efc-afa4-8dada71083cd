<template>
  <div class="p-4 rounded">
    <div class="p-field mb-4">
      <label for="itemType">Item Type</label>
      <Select
        id="itemType"
        v-model="item.type"
        :options="itemTypes"
        placeholder="Select Item Type"
        class="w-full"
      />
    </div>
    <div class="p-field mb-4">
      <label for="itemTitle">Title</label>
      <InputText
        id="itemTitle"
        v-model="item.title"
        placeholder="Enter item title"
        class="w-full"
      />
    </div>
    <div v-if="item.type === 'question'">
      <InputText v-model="item.question" placeholder="Question" class="w-full mb-2" />
      <div v-for="(option, index) in item.options" :key="index" class="flex items-center mb-2">
        <InputText v-model="option.text" placeholder="Option" class="flex-1 mr-2" />
        <RadioButton :value="index" v-model="item.correctAnswer" />
      </div>
      <Button label="Add Option" icon="pi pi-plus" @click="addOption" class="mb-2" />
    </div>
    <div v-else-if="item.type === 'text'">
      <Textarea v-model="item.content" placeholder="Text Content" rows="5" class="w-full" />
    </div>
    <div v-else-if="item.type === 'image'">
      <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
      <small>Image upload preview only (no backend)</small>
    </div>
    <div v-else-if="item.type === 'link'">
      <InputText v-model="item.url" placeholder="URL" class="w-full" />
    </div>
    <div class="mt-4">
      <Button label="Save" @click="saveItem" />
    </div>

    <!-- QR Code Display for newly created items -->
    <div v-if="lastCreatedItem" class="mt-6 p-4 border rounded-lg bg-gray-50">
      <h3 class="text-lg font-semibold mb-4">QR Code Generated!</h3>
      <QRCodeDisplay
        :bookId="route.params.id as string"
        :chapterId="props.chapterId!"
        :itemId="lastCreatedItem.id"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Item } from '../../types/item';
import Select  from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';
import QRCodeDisplay from './QRCodeDisplay.vue';

interface Props {
  chapterId?: string;
}

const props = defineProps<Props>();
const store = useMainStore();
const route = useRoute();
const item = ref<Partial<Item>>({ type: undefined, title: '', options: [], correctAnswer: undefined });
const lastCreatedItem = ref<Item | null>(null);
const itemTypes = ['question', 'text', 'image', 'link'];

watch(() => props.chapterId, () => {
  item.value = { type: undefined, title: '', options: [], correctAnswer: undefined };
  lastCreatedItem.value = null; // Clear QR code when switching chapters
});

const addOption = () => {
  item.value.options = [...(item.value.options || []), { text: '' }];
};

const saveItem = async () => {
  if (props.chapterId && item.value.type && item.value.title && route.params.id) {
    const { id, ...itemData } = { ...item.value } as Item;
    const newItem = await store.addItem(route.params.id as string, props.chapterId, itemData);
    lastCreatedItem.value = newItem;
    item.value = { type: undefined, title: '', options: [], correctAnswer: undefined };
  }
};
</script>