<template>
  <div>
    <div v-if="!book">
      <h1 class="text-2xl font-bold mb-4">No Book Selected</h1>
      <p class="text-gray-500">Please select a book from the sidebar to view its details.</p>
    </div>
    <div v-else>
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold">{{ book.title }}</h1>
        <Button
          v-tooltip:left="'Delete Book'"
          size="small"
          icon="pi pi-trash"
          severity="danger"
          @click="confirmDeleteBook"
        />
      </div>
      <div class="flex gap-4">
        <div class="w-1/3">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-bold">Chapters & Items</h2>
            <Button v-tooltip="'Add Chapter'" size="small" icon="pi pi-plus" @click="openAddChapterDialog" />
          </div>
          <Tree
            :value="treeData"
            selectionMode="single"
            v-model:selectionKeys="selectedKey"
            @node-select="onNodeSelect"
            @node-contextmenu="onNodeContextMenu"
            class="border border-surface-300 rounded-lg"
          >
            <template #default="{ node }">
              <div class="flex items-center justify-between w-full group hover:bg-gray-50 px-2 py-1 rounded">
                <span>{{ node.label }}</span>
                <Button
                  v-if="node.key !== book?.id"
                  icon="pi pi-trash"
                  size="small"
                  severity="danger"
                  outlined
                  @click.stop="confirmDelete(node)"
                  v-tooltip="'Delete'"
                  class="opacity-30 group-hover:opacity-100 hover:opacity-100 transition-opacity ml-2"
                />
              </div>
            </template>
          </Tree>
        </div>
        <div class="w-2/3">
          <div v-if="selectedItemId">
            <h2 class="text-lg font-bold mb-2">Item QR Code</h2>
            <QRCodeDisplay
              :bookId="book!.id"
              :chapterId="selectedChapterId!"
              :itemId="selectedItemId"
            />
          </div>
          <div v-else-if="selectedChapterId">
            <h2 class="text-lg font-bold mb-2">Add Item</h2>
            <ItemForm :chapterId="selectedChapterId" />
          </div>
          <p v-else class="text-gray-500">Select a chapter or item to add items.</p>
        </div>
      </div>
      <Dialog v-model:visible="showAddChapterDialog" header="Add Chapter" :style="{ width: '25vw' }">
        <InputText v-model="newChapterTitle" placeholder="Chapter Title" class="w-full" />
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showAddChapterDialog = false" />
          <Button label="Save" @click="saveChapter" />
        </template>
      </Dialog>

      <!-- Delete Confirmation Dialog -->
      <Dialog v-model:visible="showDeleteDialog" header="Confirm Delete" :style="{ width: '30vw' }">
        <p class="mb-4">{{ deleteMessage }}</p>
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showDeleteDialog = false" />
          <Button label="Delete" severity="danger" @click="performDelete" />
        </template>
      </Dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Book } from '../../types/book';
import type { TreeNode } from 'primevue/treenode';
import ItemForm from '../common/ItemForm.vue';
import QRCodeDisplay from '../common/QRCodeDisplay.vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import Tree from 'primevue/tree';
import InputText from 'primevue/inputtext';
import { NodeService } from '../../service/NodeService';
import type { TreeSelectionKeys } from 'primevue/tree';

const store = useMainStore();
const route = useRoute();
const router = useRouter();
const book = computed<Book | undefined>(() => store.getBook(route.params.id as string));
const selectedKey = ref<TreeSelectionKeys | undefined>(undefined);
const selectedChapterId = ref<string | null>(null);
const selectedItemId = ref<string | null>(null);
const showAddChapterDialog = ref(false);
const newChapterTitle = ref('');
const treeData = ref<TreeNode[]>([]);

// Delete functionality
const showDeleteDialog = ref(false);
const deleteMessage = ref('');
const nodeToDelete = ref<TreeNode | null>(null);

// Load tree data when book changes
watch(
  book,
  async (newBook) => {
    if (newBook) {
      treeData.value = await NodeService.getTreeNodes(newBook);
    } else {
      treeData.value = [];
    }
  },
  { immediate: true }
);

const onNodeSelect = (node: TreeNode) => {
  if (!book.value || !node.key) {
    selectedKey.value = undefined;
    selectedChapterId.value = null;
    selectedItemId.value = null;
    return;
  }

  selectedKey.value = { [node.key]: { checked: true } };

  if (node.children) {
    // Chapter (has children)
    selectedChapterId.value = node.key;
    selectedItemId.value = null;
  } else {
    // Item (no children)
    const chapter = book.value.chapters.find(ch =>
      ch.items.some(item => item.id === node.key)
    );
    selectedChapterId.value = chapter?.id ?? null;
    selectedItemId.value = node.key;
  }
};

const openAddChapterDialog = () => {
  newChapterTitle.value = '';
  showAddChapterDialog.value = true;
};

const saveChapter = async () => {
  if (book.value && newChapterTitle.value) {
    await store.addChapter(book.value.id, { title: newChapterTitle.value });
    showAddChapterDialog.value = false;
  }
};

// Delete functionality
const onNodeContextMenu = (event: any) => {
  // Context menu functionality can be added here if needed
  console.log('Context menu for node:', event.node);
};

const confirmDelete = (node: TreeNode) => {
  nodeToDelete.value = node;

  if (node.children && node.children.length > 0) {
    // Chapter with items
    deleteMessage.value = `Are you sure you want to delete the chapter "${node.label}" and all its items? This action cannot be undone.`;
  } else if (node.children) {
    // Empty chapter
    deleteMessage.value = `Are you sure you want to delete the chapter "${node.label}"?`;
  } else {
    // Item
    deleteMessage.value = `Are you sure you want to delete the item "${node.label}"?`;
  }

  showDeleteDialog.value = true;
};

const confirmDeleteBook = () => {
  if (!book.value) return;

  nodeToDelete.value = {
    key: book.value.id,
    label: book.value.title,
    data: 'book'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the book "${book.value.title}" and all its chapters and items? This action cannot be undone.`;
  showDeleteDialog.value = true;
};

const performDelete = async () => {
  if (!nodeToDelete.value) {
    return;
  }

  const node = nodeToDelete.value;

  try {
    if (node.data === 'book') {
      // It's a book
      await store.deleteBook(node.key);
      // Navigate to home since the book is deleted
      router.push('/');
    } else if (node.children !== undefined) {
      // It's a chapter
      if (!book.value) return;
      await store.deleteChapter(book.value.id, node.key);

      // Clear selection if deleted chapter was selected
      if (selectedChapterId.value === node.key) {
        selectedChapterId.value = null;
        selectedItemId.value = null;
        selectedKey.value = undefined;
      }
    } else {
      // It's an item
      if (!book.value) return;
      const chapter = book.value.chapters.find(ch =>
        ch.items.some(item => item.id === node.key)
      );

      if (chapter) {
        await store.deleteItem(book.value.id, chapter.id, node.key);

        // Clear selection if deleted item was selected
        if (selectedItemId.value === node.key) {
          selectedItemId.value = null;
          selectedKey.value = undefined;
        }
      }
    }

    showDeleteDialog.value = false;
    nodeToDelete.value = null;
  } catch (error) {
    console.error('Error deleting:', error);
    // You could show an error message here
  }
};
</script>